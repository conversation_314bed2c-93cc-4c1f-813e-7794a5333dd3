import { useState, useMemo } from "react";
import MetricCard from "@/components/Charts/MetricCard";
import MetricDetails from "@/components/Charts/MetricDetails";

const OperationalTasksCharts = () => {
  const [openDetailsId, setOpenDetailsId] = useState<string | null>(null);
  const [selectedPeriods, setSelectedPeriods] = useState<Record<string, string>>({});

  // Operational Tasks metrics data
  const overviewMetrics = [
    {
      id: "tasks-completed-on-time",
      title: "% of Tasks Completed On-Time",
      value: "91",
      unit: "tasks (91%)",
      target: 100,
      targetPercentage: 9,
      trend: [87, 88, 89, 90, 91, 92, 91, 90, 91, 92, 93, 91],
      isImproving: true
    },
    {
      id: "daily-tasks-completion",
      title: "% of Daily Tasks Completion Rate",
      value: "96",
      unit: "tasks (96%)",
      target: 100,
      targetPercentage: 4,
      trend: [92, 93, 94, 95, 96, 97, 96, 95, 96, 97, 98, 96],
      isImproving: true
    },
    {
      id: "tasks-overdue",
      title: "% of Tasks Overdue",
      value: "4",
      unit: "tasks (4%)",
      target: 0,
      targetPercentage: 4,
      trend: [7, 6, 5, 4, 3, 4, 5, 4, 3, 4, 5, 4],
      isImproving: false
    },
    {
      id: "critical-tasks-pending",
      title: "Number of Critical Tasks Pending",
      value: "8",
      unit: "tasks",
      target: 0,
      targetPercentage: 100,
      trend: [12, 11, 10, 9, 8, 7, 8, 9, 8, 7, 8, 8],
      isImproving: false
    }
  ];

  const handleToggleDetails = (metricId: string) => {
    setOpenDetailsId(openDetailsId === metricId ? null : metricId);
  };

  const handlePeriodChange = (metricId: string, period: string) => {
    setSelectedPeriods(prev => ({
      ...prev,
      [metricId]: period
    }));
  };

  const getSelectedPeriod = (metricId: string) => {
    return selectedPeriods[metricId] || "1Y";
  };

  return (
    <div className="space-y-6">
      {/* Headline Metrics */}
      <div className="grid grid-cols-4 gap-4">
        {overviewMetrics.map((metric) => (
          <MetricCard
            key={metric.id}
            {...metric}
            showDetails={openDetailsId === metric.id}
            onToggleDetails={() => handleToggleDetails(metric.id)}
            selectedPeriod={getSelectedPeriod(metric.id)}
            onPeriodChange={(period) => handlePeriodChange(metric.id, period)}
          />
        ))}
      </div>

      {/* Details Section - Rendered below the grid when a card is selected */}
      {openDetailsId && (() => {
        const selectedMetric = overviewMetrics.find(m => m.id === openDetailsId);
        return selectedMetric ? (
          <MetricDetails
            title={selectedMetric.title}
            value={selectedMetric.value}
            unit={selectedMetric.unit}
            targetPercentage={selectedMetric.targetPercentage}
            trend={selectedMetric.trend}
            isImproving={selectedMetric.isImproving}
            selectedPeriod={getSelectedPeriod(openDetailsId)}
          />
        ) : null;
      })()}
    </div>
  );
};

export default OperationalTasksCharts;
