import { useMemo } from "react";
import { ArrowU<PERSON>, ArrowDown } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import ExportButton from "@/components/Charts/ExportButton";

interface TrendDataPoint {
  date: string;
  value: number;
}

interface MetricDetailsProps {
  title: string;
  value: string;
  unit: string;
  targetPercentage: number;
  trend: number[] | TrendDataPoint[];
  isImproving: boolean;
  selectedPeriod: string;
}

const MetricDetails = ({
  title,
  value,
  unit,
  targetPercentage,
  trend,
  isImproving,
  selectedPeriod
}: MetricDetailsProps) => {
  // Convert trend data to consistent format and filter by time period
  const filteredTrendData = useMemo(() => {
    // Convert to TrendDataPoint format if it's just numbers
    let trendData: TrendDataPoint[];

    if (typeof trend[0] === 'number') {
      // Generate dates for the last 12 months if it's just numbers
      const now = new Date();
      trendData = (trend as number[]).map((value, index) => {
        const date = new Date(now);
        date.setMonth(date.getMonth() - (trend.length - 1 - index));
        return {
          date: date.toISOString().slice(0, 7), // YYYY-MM format
          value: value
        };
      });
    } else {
      trendData = trend as TrendDataPoint[];
    }

    // Filter based on selected period
    const now = new Date();
    let cutoffDate: Date;

    switch (selectedPeriod) {
      case '3M':
        cutoffDate = new Date(now);
        cutoffDate.setMonth(cutoffDate.getMonth() - 3);
        break;
      case '6M':
        cutoffDate = new Date(now);
        cutoffDate.setMonth(cutoffDate.getMonth() - 6);
        break;
      case '1Y':
        cutoffDate = new Date(now);
        cutoffDate.setFullYear(cutoffDate.getFullYear() - 1);
        break;
      case 'Max':
      default:
        return trendData; // Return all data
    }

    return trendData.filter(point => new Date(point.date) >= cutoffDate);
  }, [trend, selectedPeriod]);

  // Extract values for calculations
  const trendValues = filteredTrendData.map(point => point.value);
  const maxTrend = Math.max(...trendValues);
  const minTrend = Math.min(...trendValues);

  // Modern color scheme - softer, more professional
  const changeColor = isImproving ? 'text-emerald-600' : 'text-rose-600';

  return (
    <div className="mt-6 p-6 bg-blue-50/50 rounded-xl border border-blue-200 w-full shadow-md animate-in slide-in-from-top-2 duration-300">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h4 className="text-lg font-semibold text-gray-900">Detailed Data - {title}</h4>
          <div className="text-sm text-gray-500 mt-1">
            {filteredTrendData.length} data points • {selectedPeriod} period
          </div>
        </div>
        <ExportButton
          data={filteredTrendData}
          title={title}
          unit={unit}
          targetPercentage={targetPercentage}
          isImproving={isImproving}
        />
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-4 gap-6 mb-6 p-4 bg-white rounded-lg border border-gray-100">
        <div className="text-center">
          <div className="text-sm text-gray-500 font-medium">Current</div>
          <div className="text-lg font-semibold text-gray-900">{value} {unit}</div>
        </div>
        <div className="text-center">
          <div className="text-sm text-gray-500 font-medium">Target</div>
          <div className={`text-lg font-semibold ${changeColor}`}>
            {isImproving ? '+' : ''}{targetPercentage}%
          </div>
        </div>
        <div className="text-center">
          <div className="text-sm text-gray-500 font-medium">Min</div>
          <div className="text-lg font-semibold text-gray-900">
            {minTrend > 1000 ? `${(minTrend/1000).toFixed(1)}k` : Math.round(minTrend)}
          </div>
        </div>
        <div className="text-center">
          <div className="text-sm text-gray-500 font-medium">Max</div>
          <div className="text-lg font-semibold text-gray-900">
            {maxTrend > 1000 ? `${(maxTrend/1000).toFixed(1)}k` : Math.round(maxTrend)}
          </div>
        </div>
      </div>

      {/* Data Table */}
      <div className="bg-white rounded-lg border border-slate-200 overflow-hidden shadow-sm">
        <Table>
          <TableHeader>
            <TableRow className="bg-slate-50 border-b border-slate-200">
              <TableHead className="text-sm font-semibold text-gray-700">Period</TableHead>
              <TableHead className="text-sm font-semibold text-gray-700">Value</TableHead>
              <TableHead className="text-sm font-semibold text-gray-700">Change</TableHead>
              <TableHead className="text-sm font-semibold text-gray-700">Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredTrendData.map((dataPoint, index) => {
              const previousValue = index > 0 ? filteredTrendData[index - 1].value : null;
              const change = previousValue !== null ? ((dataPoint.value - previousValue) / previousValue) * 100 : null;
              const isPositiveChange = change !== null ? change > 0 : false;

              // Format date based on selected period
              const formatDate = (dateStr: string) => {
                const date = new Date(dateStr);
                if (selectedPeriod === '3M' || selectedPeriod === '6M') {
                  return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
                } else {
                  return date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
                }
              };

              // Determine if this change is good or bad based on metric type
              const isGoodChange = change !== null ? (
                title.toLowerCase().includes('emission') ||
                title.toLowerCase().includes('energy') ||
                title.toLowerCase().includes('water') ||
                title.toLowerCase().includes('waste')
              ) ? change < 0 : change > 0 : null;

              return (
                <TableRow key={index} className="hover:bg-gray-50">
                  <TableCell className="text-sm font-medium text-gray-900">
                    {formatDate(dataPoint.date)}
                  </TableCell>
                  <TableCell className="text-sm font-semibold text-gray-900">
                    {dataPoint.value > 1000 ? `${(dataPoint.value/1000).toFixed(1)}k` : dataPoint.value.toFixed(1)} {unit}
                  </TableCell>
                  <TableCell className="text-sm">
                    {change !== null ? (
                      <span className={`font-medium ${isPositiveChange ? 'text-blue-600' : 'text-blue-600'}`}>
                        {isPositiveChange ? '+' : ''}{change.toFixed(1)}%
                      </span>
                    ) : (
                      <span className="text-gray-400">-</span>
                    )}
                  </TableCell>
                  <TableCell className="text-sm">
                    {isGoodChange !== null ? (
                      <div className={`inline-flex items-center space-x-1 px-3 py-1 rounded-full ${
                        isGoodChange ? 'bg-emerald-50 text-emerald-700' : 'bg-rose-50 text-rose-700'
                      }`}>
                        {isGoodChange ? (
                          <ArrowUp className="w-4 h-4" />
                        ) : (
                          <ArrowDown className="w-4 h-4" />
                        )}
                        <span className="font-medium text-sm">
                          {isGoodChange ? 'Good' : 'Watch'}
                        </span>
                      </div>
                    ) : (
                      <span className="text-gray-400 text-sm">-</span>
                    )}
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default MetricDetails;
