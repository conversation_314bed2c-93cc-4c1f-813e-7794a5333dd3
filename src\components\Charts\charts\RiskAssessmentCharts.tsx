import { useState, useMemo } from "react";
import MetricCard from "@/components/Charts/MetricCard";
import MetricDetails from "@/components/Charts/MetricDetails";

const RiskAssessmentCharts = () => {
  const [openDetailsId, setOpenDetailsId] = useState<string | null>(null);
  const [selectedPeriods, setSelectedPeriods] = useState<Record<string, string>>({});

  // Risk Assessment metrics data
  const overviewMetrics = [
    {
      id: "risk-assessments-completed",
      title: "% of Risk Assessments Completed On-Time",
      value: "87",
      unit: "assessments (87%)",
      target: 100,
      targetPercentage: 13,
      trend: [82, 84, 85, 86, 87, 88, 87, 86, 87, 88, 89, 87],
      isImproving: true
    },
    {
      id: "high-risk-items-resolved",
      title: "% of High Risk Items Resolved Within SLA",
      value: "92",
      unit: "items (92%)",
      target: 100,
      targetPercentage: 8,
      trend: [88, 89, 90, 91, 92, 93, 92, 91, 92, 93, 94, 92],
      isImproving: true
    },
    {
      id: "risk-assessments-overdue",
      title: "% of Risk Assessments Overdue",
      value: "5",
      unit: "assessments (5%)",
      target: 0,
      targetPercentage: 5,
      trend: [8, 7, 6, 5, 4, 5, 6, 5, 4, 5, 6, 5],
      isImproving: false
    },
    {
      id: "critical-risks-identified",
      title: "Number of Critical Risks Identified",
      value: "12",
      unit: "risks",
      target: 0,
      targetPercentage: 100,
      trend: [15, 14, 13, 12, 11, 12, 13, 12, 11, 12, 13, 12],
      isImproving: false
    }
  ];

  const handleToggleDetails = (metricId: string) => {
    setOpenDetailsId(openDetailsId === metricId ? null : metricId);
  };

  const handlePeriodChange = (metricId: string, period: string) => {
    setSelectedPeriods(prev => ({
      ...prev,
      [metricId]: period
    }));
  };

  const getSelectedPeriod = (metricId: string) => {
    return selectedPeriods[metricId] || "1Y";
  };

  return (
    <div className="space-y-6">
      {/* Headline Metrics */}
      <div className="grid grid-cols-4 gap-4">
        {overviewMetrics.map((metric) => (
          <MetricCard
            key={metric.id}
            {...metric}
            showDetails={openDetailsId === metric.id}
            onToggleDetails={() => handleToggleDetails(metric.id)}
            selectedPeriod={getSelectedPeriod(metric.id)}
            onPeriodChange={(period) => handlePeriodChange(metric.id, period)}
          />
        ))}
      </div>

      {/* Details Section - Rendered below the grid when a card is selected */}
      {openDetailsId && (() => {
        const selectedMetric = overviewMetrics.find(m => m.id === openDetailsId);
        return selectedMetric ? (
          <MetricDetails
            title={selectedMetric.title}
            value={selectedMetric.value}
            unit={selectedMetric.unit}
            targetPercentage={selectedMetric.targetPercentage}
            trend={selectedMetric.trend}
            isImproving={selectedMetric.isImproving}
            selectedPeriod={getSelectedPeriod(openDetailsId)}
          />
        ) : null;
      })()}
    </div>
  );
};

export default RiskAssessmentCharts;
