import { useState, useMemo } from "react";
import MetricCard from "@/components/Charts/MetricCard";
import MetricDetails from "@/components/Charts/MetricDetails";

const IncidentInvestigationCharts = () => {
  const [openDetailsId, setOpenDetailsId] = useState<string | null>(null);
  const [selectedPeriods, setSelectedPeriods] = useState<Record<string, string>>({});

  // Incident Investigation metrics data
  const overviewMetrics = [
    {
      id: "incidents-investigated-on-time",
      title: "% of Incidents Investigated On-Time",
      value: "85",
      unit: "incidents (85%)",
      target: 100,
      targetPercentage: 15,
      trend: [80, 81, 82, 83, 84, 85, 84, 83, 84, 85, 86, 85],
      isImproving: true
    },
    {
      id: "corrective-actions-implemented",
      title: "% of Corrective Actions Implemented",
      value: "78",
      unit: "actions (78%)",
      target: 100,
      targetPercentage: 22,
      trend: [74, 75, 76, 77, 78, 79, 78, 77, 78, 79, 80, 78],
      isImproving: true
    },
    {
      id: "investigations-overdue",
      title: "% of Investigations Overdue",
      value: "8",
      unit: "investigations (8%)",
      target: 0,
      targetPercentage: 8,
      trend: [12, 11, 10, 9, 8, 7, 8, 9, 8, 7, 8, 8],
      isImproving: false
    },
    {
      id: "repeat-incidents",
      title: "Number of Repeat Incidents",
      value: "3",
      unit: "incidents",
      target: 0,
      targetPercentage: 100,
      trend: [6, 5, 4, 3, 2, 3, 4, 3, 2, 3, 4, 3],
      isImproving: false
    }
  ];

  const handleToggleDetails = (metricId: string) => {
    setOpenDetailsId(openDetailsId === metricId ? null : metricId);
  };

  const handlePeriodChange = (metricId: string, period: string) => {
    setSelectedPeriods(prev => ({
      ...prev,
      [metricId]: period
    }));
  };

  const getSelectedPeriod = (metricId: string) => {
    return selectedPeriods[metricId] || "1Y";
  };

  return (
    <div className="space-y-6">
      {/* Headline Metrics */}
      <div className="grid grid-cols-4 gap-4">
        {overviewMetrics.map((metric) => (
          <MetricCard
            key={metric.id}
            {...metric}
            showDetails={openDetailsId === metric.id}
            onToggleDetails={() => handleToggleDetails(metric.id)}
            selectedPeriod={getSelectedPeriod(metric.id)}
            onPeriodChange={(period) => handlePeriodChange(metric.id, period)}
          />
        ))}
      </div>

      {/* Details Section - Rendered below the grid when a card is selected */}
      {openDetailsId && (() => {
        const selectedMetric = overviewMetrics.find(m => m.id === openDetailsId);
        return selectedMetric ? (
          <MetricDetails
            title={selectedMetric.title}
            value={selectedMetric.value}
            unit={selectedMetric.unit}
            targetPercentage={selectedMetric.targetPercentage}
            trend={selectedMetric.trend}
            isImproving={selectedMetric.isImproving}
            selectedPeriod={getSelectedPeriod(openDetailsId)}
          />
        ) : null;
      })()}
    </div>
  );
};

export default IncidentInvestigationCharts;
